import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { ArrowRightIcon } from "lucide-react";

export function Hero() {
	return (
		<div className="relative min-h-screen flex items-center justify-center overflow-x-hidden bg-linear-to-b from-0% from-card to-[50vh] to-background">
			<div className="absolute left-1/2 z-10 ml-[-500px] h-[500px] w-[1000px] rounded-full bg-linear-to-r from-primary to-bg opacity-20 blur-[150px]" />
			<div className="container relative z-20 text-center">
				<h1 className="mx-auto max-w-4xl text-balance font-bold text-5xl lg:text-7xl">
					Turn Your Google Business Link Into a Website in Seconds
				</h1>

				<p className="mx-auto mt-6 max-w-lg text-balance text-foreground/60 text-xl">
					Just paste your link, choose a template, and launch!
				</p>

				<div className="mt-8 flex flex-col items-center justify-center gap-4 md:flex-row md:max-w-lg md:mx-auto">
					<Input
						type="url"
						placeholder="Paste your Google Maps business link here..."
						className="flex-1 h-12 text-base"
					/>
					<Button size="lg" variant="primary" className="h-12 px-8">
						Generate My Site
						<ArrowRightIcon className="ml-2 size-4" />
					</Button>
				</div>
			</div>
		</div>
	);
}
