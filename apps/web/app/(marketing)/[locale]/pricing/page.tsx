import { PricingTable } from "@saas/payments/components/PricingTable";
import { setRequestLocale } from "next-intl/server";
import { getTranslations } from "next-intl/server";

export default async function PricingPage({
	params,
}: {
	params: Promise<{ locale: string }>;
}) {
	const { locale } = await params;
	setRequestLocale(locale);

	const t = await getTranslations();

	return (
		<div className="container max-w-5xl pt-32 pb-16">
			<div className="mb-6 lg:text-center">
				<h1 className="font-bold text-4xl lg:text-5xl">
					{t("pricing.title")}
				</h1>
				<p className="mt-3 text-lg opacity-50">
					{t("pricing.description")}
				</p>
			</div>

			<PricingTable />
		</div>
	);
}
