import { setRequestLocale } from "next-intl/server";

export default async function PricingPage({
	params,
}: {
	params: Promise<{ locale: string }>;
}) {
	const { locale } = await params;
	setRequestLocale(locale);

	return (
		<div className="container max-w-4xl py-24">
			<div className="text-center mb-16">
				<h1 className="font-bold text-4xl lg:text-6xl mb-6">
					Simple, Transparent Pricing
				</h1>
				<p className="text-foreground/60 text-xl max-w-2xl mx-auto">
					Choose the perfect plan for your business. Start free and upgrade as you grow.
				</p>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
				{/* Free Plan */}
				<div className="border rounded-2xl p-8 bg-card">
					<div className="text-center mb-8">
						<h3 className="font-bold text-2xl mb-2">Free</h3>
						<div className="mb-4">
							<span className="text-4xl font-bold">$0</span>
							<span className="text-foreground/60">/month</span>
						</div>
						<p className="text-foreground/60">Perfect for trying out our service</p>
					</div>
					
					<ul className="space-y-3 mb-8">
						<li className="flex items-center gap-3">
							<span className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
								<span className="w-2 h-2 rounded-full bg-green-600"></span>
							</span>
							<span>1 website</span>
						</li>
						<li className="flex items-center gap-3">
							<span className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
								<span className="w-2 h-2 rounded-full bg-green-600"></span>
							</span>
							<span>Basic templates</span>
						</li>
						<li className="flex items-center gap-3">
							<span className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
								<span className="w-2 h-2 rounded-full bg-green-600"></span>
							</span>
							<span>Community support</span>
						</li>
					</ul>
					
					<button className="w-full py-3 px-6 border border-border rounded-lg hover:bg-muted transition-colors">
						Get Started
					</button>
				</div>

				{/* Pro Plan */}
				<div className="border-2 border-primary rounded-2xl p-8 bg-card relative">
					<div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
						<span className="bg-primary text-primary-foreground px-4 py-1 rounded-full text-sm font-medium">
							Most Popular
						</span>
					</div>
					
					<div className="text-center mb-8">
						<h3 className="font-bold text-2xl mb-2">Pro</h3>
						<div className="mb-4">
							<span className="text-4xl font-bold">$19</span>
							<span className="text-foreground/60">/month</span>
						</div>
						<p className="text-foreground/60">Best for growing businesses</p>
					</div>
					
					<ul className="space-y-3 mb-8">
						<li className="flex items-center gap-3">
							<span className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
								<span className="w-2 h-2 rounded-full bg-green-600"></span>
							</span>
							<span>10 websites</span>
						</li>
						<li className="flex items-center gap-3">
							<span className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
								<span className="w-2 h-2 rounded-full bg-green-600"></span>
							</span>
							<span>Premium templates</span>
						</li>
						<li className="flex items-center gap-3">
							<span className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
								<span className="w-2 h-2 rounded-full bg-green-600"></span>
							</span>
							<span>Custom domain</span>
						</li>
						<li className="flex items-center gap-3">
							<span className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
								<span className="w-2 h-2 rounded-full bg-green-600"></span>
							</span>
							<span>Priority support</span>
						</li>
					</ul>
					
					<button className="w-full py-3 px-6 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
						Start Pro Trial
					</button>
				</div>

				{/* Enterprise Plan */}
				<div className="border rounded-2xl p-8 bg-card">
					<div className="text-center mb-8">
						<h3 className="font-bold text-2xl mb-2">Enterprise</h3>
						<div className="mb-4">
							<span className="text-4xl font-bold">$99</span>
							<span className="text-foreground/60">/month</span>
						</div>
						<p className="text-foreground/60">For large organizations</p>
					</div>
					
					<ul className="space-y-3 mb-8">
						<li className="flex items-center gap-3">
							<span className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
								<span className="w-2 h-2 rounded-full bg-green-600"></span>
							</span>
							<span>Unlimited websites</span>
						</li>
						<li className="flex items-center gap-3">
							<span className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
								<span className="w-2 h-2 rounded-full bg-green-600"></span>
							</span>
							<span>Custom templates</span>
						</li>
						<li className="flex items-center gap-3">
							<span className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
								<span className="w-2 h-2 rounded-full bg-green-600"></span>
							</span>
							<span>White-label solution</span>
						</li>
						<li className="flex items-center gap-3">
							<span className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
								<span className="w-2 h-2 rounded-full bg-green-600"></span>
							</span>
							<span>Dedicated support</span>
						</li>
					</ul>
					
					<button className="w-full py-3 px-6 border border-border rounded-lg hover:bg-muted transition-colors">
						Contact Sales
					</button>
				</div>
			</div>

			<div className="text-center">
				<h2 className="font-bold text-2xl mb-4">Frequently Asked Questions</h2>
				<p className="text-foreground/60 mb-8">
					Have questions? We have answers.
				</p>
				
				<div className="max-w-2xl mx-auto space-y-4">
					<details className="border rounded-lg p-4 text-left">
						<summary className="font-medium cursor-pointer">
							Can I change my plan anytime?
						</summary>
						<p className="mt-2 text-foreground/60">
							Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
						</p>
					</details>
					
					<details className="border rounded-lg p-4 text-left">
						<summary className="font-medium cursor-pointer">
							Is there a free trial?
						</summary>
						<p className="mt-2 text-foreground/60">
							Yes, all paid plans come with a 14-day free trial. No credit card required.
						</p>
					</details>
					
					<details className="border rounded-lg p-4 text-left">
						<summary className="font-medium cursor-pointer">
							What payment methods do you accept?
						</summary>
						<p className="mt-2 text-foreground/60">
							We accept all major credit cards, PayPal, and bank transfers for enterprise plans.
						</p>
					</details>
				</div>
			</div>
		</div>
	);
}
