import { setRequestLocale } from "next-intl/server";

export default async function FAQPage({
	params,
}: {
	params: Promise<{ locale: string }>;
}) {
	const { locale } = await params;
	setRequestLocale(locale);

	const faqs = [
		{
			category: "Getting Started",
			questions: [
				{
					question: "How does it work?",
					answer: "Simply paste your Google Business link, choose from our beautiful templates, and your website will be generated automatically. The entire process takes less than 60 seconds."
				},
				{
					question: "Do I need technical skills?",
					answer: "Not at all! Our platform is designed for everyone. No coding, no design skills, and no technical knowledge required."
				},
				{
					question: "What information do you extract from my Google Business listing?",
					answer: "We extract your business name, address, phone number, hours, photos, reviews, and other public information to create your website."
				},
				{
					question: "Can I customize my website after it's generated?",
					answer: "Yes! You can edit text, change colors, add or remove sections, upload new images, and customize your website to match your brand."
				}
			]
		},
		{
			category: "Pricing & Plans",
			questions: [
				{
					question: "Is there a free plan?",
					answer: "Yes! Our free plan allows you to create 1 website with basic templates and community support. Perfect for trying out our service."
				},
				{
					question: "Can I upgrade or downgrade my plan?",
					answer: "Absolutely! You can change your plan at any time. Upgrades take effect immediately, and downgrades take effect at the end of your current billing cycle."
				},
				{
					question: "Do you offer refunds?",
					answer: "Yes, we offer a 30-day money-back guarantee. If you're not satisfied with our service, we'll refund your payment in full."
				},
				{
					question: "Are there any setup fees?",
					answer: "No setup fees, no hidden costs. You only pay the monthly or annual subscription fee for your chosen plan."
				}
			]
		},
		{
			category: "Features & Functionality",
			questions: [
				{
					question: "Can I use my own domain?",
					answer: "Yes! Pro and Enterprise plans include custom domain support. You can connect your existing domain or purchase a new one through our platform."
				},
				{
					question: "Are the websites mobile-friendly?",
					answer: "Absolutely! All our templates are fully responsive and optimized for mobile devices, tablets, and desktops."
				},
				{
					question: "Can I add online booking or e-commerce?",
					answer: "Yes! Our Pro and Enterprise plans include integrations with popular booking systems and e-commerce platforms."
				},
				{
					question: "How fast are the websites?",
					answer: "Our websites are optimized for speed and performance. They typically load in under 2 seconds and score 90+ on Google PageSpeed Insights."
				}
			]
		},
		{
			category: "Support & Maintenance",
			questions: [
				{
					question: "What kind of support do you provide?",
					answer: "Free plans include community support. Pro plans get priority email support, and Enterprise plans include dedicated phone support and a customer success manager."
				},
				{
					question: "Do you provide website maintenance?",
					answer: "Yes! We handle all technical maintenance, security updates, and hosting. Your website is always up-to-date and secure."
				},
				{
					question: "What if my Google Business information changes?",
					answer: "You can easily sync your website with your updated Google Business listing, or manually update the information through our editor."
				},
				{
					question: "Can I export my website?",
					answer: "Enterprise plans include website export functionality. You can download your website files and host them anywhere you like."
				}
			]
		}
	];

	return (
		<div className="container max-w-4xl py-24">
			<div className="text-center mb-16">
				<h1 className="font-bold text-4xl lg:text-6xl mb-6">
					Frequently Asked Questions
				</h1>
				<p className="text-foreground/60 text-xl max-w-2xl mx-auto">
					Everything you need to know about turning your Google Business listing into a professional website.
				</p>
			</div>

			<div className="space-y-12">
				{faqs.map((category, categoryIndex) => (
					<div key={categoryIndex}>
						<h2 className="font-bold text-2xl mb-6 text-primary">
							{category.category}
						</h2>
						
						<div className="space-y-4">
							{category.questions.map((faq, faqIndex) => (
								<details 
									key={faqIndex}
									className="border rounded-lg p-6 bg-card hover:bg-card/80 transition-colors"
								>
									<summary className="font-medium text-lg cursor-pointer hover:text-primary transition-colors">
										{faq.question}
									</summary>
									<p className="mt-4 text-foreground/70 leading-relaxed">
										{faq.answer}
									</p>
								</details>
							))}
						</div>
					</div>
				))}
			</div>

			<div className="mt-16 text-center bg-muted rounded-2xl p-8">
				<h2 className="font-bold text-2xl mb-4">
					Still have questions?
				</h2>
				<p className="text-foreground/60 mb-6">
					Can't find the answer you're looking for? Our support team is here to help.
				</p>
				<div className="flex flex-col sm:flex-row gap-4 justify-center">
					<button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
						Contact Support
					</button>
					<button className="px-6 py-3 border border-border rounded-lg hover:bg-background transition-colors">
						Schedule a Demo
					</button>
				</div>
			</div>
		</div>
	);
}
