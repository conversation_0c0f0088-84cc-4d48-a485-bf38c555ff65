@theme {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-success: var(--success);
	--color-success-foreground: var(--success-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-highlight: var(--highlight);
	--color-highlight-foreground: var(--highlight-foreground);
	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);
 
	--font-sans: var(--font-geist-sans);
	--font-heading: var(--font-bricolage-grotesque);

	--animation-accordion-down: accordion-down 0.2s ease-out;
	--animation-accordion-up: accordion-up 0.2s ease-out;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}
	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0;
		}
	}
}

@layer base {
	:root {
		--border: #f0e6d6;
		--input: #e8ddd0;
		--ring: #8b7355;
		--background: #fefcf9;
		--foreground: #4a453e;
		--primary: #8b7355;
		--primary-foreground: #fefcf9;
		--secondary: #d4c4a8;
		--secondary-foreground: #4a453e;
		--destructive: #d97757;
		--destructive-foreground: #ffffff;
		--success: #6b9080;
		--success-foreground: #ffffff;
		--muted: #f0e6d6;
		--muted-foreground: #6b5d4f;
		--accent: #a8c8a0;
		--accent-foreground: #2d4a2b;
		--popover: #fefcf9;
		--popover-foreground: #4a453e;
		--card: #fefcf9;
		--card-foreground: #4a453e;
		--highlight: #c9a876;
		--highlight-foreground: #4a453e;
		--radius: 1rem;

		/* fumadocs */
		--fd-banner-height: 4.5rem;
	}

	.dark {
		--border: #3d3530;
		--input: #4a453e;
		--ring: #c9a876;
		--background: #1a1612;
		--foreground: #f0e6d6;
		--primary: #c9a876;
		--primary-foreground: #1a1612;
		--secondary: #6b5d4f;
		--secondary-foreground: #f0e6d6;
		--destructive: #d97757;
		--destructive-foreground: #ffffff;
		--success: #6b9080;
		--success-foreground: #ffffff;
		--muted: #3d3530;
		--muted-foreground: #c9a876;
		--accent: #7a9471;
		--accent-foreground: #e8f5e6;
		--popover: #2a241f;
		--popover-foreground: #f0e6d6;
		--card: #2a241f;
		--card-foreground: #f0e6d6;
		--highlight: #c9a876;
		--highlight-foreground: #1a1612;
	}

	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		@apply border-border;
	}

	h1, h2, h3, h4, h5, h6 {
		font-family: var(--font-heading), var(--font-sans), sans-serif;
	}
}

@utility container {
	margin-inline: auto;
	padding-inline: 1.5rem;
	width: 100%;
	max-width: var(--container-7xl);
}

@utility no-scrollbar {
	&::-webkit-scrollbar {
		display: none;
	}
	-ms-overflow-style: none;
	scrollbar-width: none;
}
