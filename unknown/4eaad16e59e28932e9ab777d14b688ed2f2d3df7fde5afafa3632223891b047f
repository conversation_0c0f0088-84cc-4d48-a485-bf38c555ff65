import { FaqSection } from "@marketing/home/<USER>/FaqSection";
import { Features } from "@marketing/home/<USER>/Features";
import { <PERSON> } from "@marketing/home/<USER>/Hero";
import { Newsletter } from "@marketing/home/<USER>/Newsletter";
import { PricingSection } from "@marketing/home/<USER>/PricingSection";
import { setRequestLocale } from "next-intl/server";

export default async function Home({
	params,
}: {
	params: Promise<{ locale: string }>;
}) {
	const { locale } = await params;
	setRequestLocale(locale);

	return (
		<>
			<Hero />
			<Features />
			<PricingSection />
			<FaqSection />
			<Newsletter />
		</>
	);
}
