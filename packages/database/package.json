{"dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.9.0", "@repo/config": "workspace:*", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "pg": "^8.16.0", "zod": "^3.25.55"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "22.15.30", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.31.1", "prisma": "^6.9.0", "prisma-json-types-generator": "^3.4.2", "zod-prisma-types": "^3.2.4"}, "main": "./index.ts", "name": "@repo/database", "scripts": {"generate": "prisma generate --no-hints --schema=./prisma/schema.prisma", "push": "dotenv -c -e ../../.env -- prisma db push --skip-generate --schema=./prisma/schema.prisma", "migrate": "dotenv -c -e ../../.env -- prisma migrate dev --schema=./prisma/schema.prisma", "studio": "dotenv -c -e ../../.env -- prisma studio --schema=./prisma/schema.prisma", "type-check": "tsc --noEmit"}, "types": "./**/.tsx", "version": "0.0.0"}