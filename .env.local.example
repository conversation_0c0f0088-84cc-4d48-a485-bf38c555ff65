# Database
DATABASE_URL="postgresql://postgres.okiqgwkzlomncicoufmv:<EMAIL>:6543/postgres?pgbouncer=true"
# ... if you use Supabase
DIRECT_URL="postgresql://postgres.okiqgwkzlomncicoufmv:<EMAIL>:5432/postgres"

# Site url
NEXT_PUBLIC_SITE_URL="http://localhost:3000"

# Authentication
BETTER_AUTH_SECRET="paHFrBaZdHUahefznsh3ACaVM4uId6CT"
# ... for Github
GITHUB_CLIENT_ID="YOUR_GITHUB_CLIENT_ID"
GITHUB_CLIENT_SECRET="YOUR_GITHUB_CLIENT_SECRET"
# ... for Google
GOOGLE_CLIENT_ID="************-ks2k0541ip8a311kk6u1qn2clh7hiu18.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# Mails
# ... with nodemailer
MAIL_HOST="localhost"
MAIL_PORT="1025"
MAIL_USER=""
MAIL_PASS=""
# ... with Plunk
PLUNK_API_KEY=""
# ... with Resend
RESEND_API_KEY="re_6rWdawbS_NTcZUB3KoXkrb3W2Y7xBBwJv"
# ... with Postmark
POSTMARK_SERVER_TOKEN=""
# ... with Mailgun
MAILGUN_API_KEY=""
MAILGUN_DOMAIN=""

# Payments
# ... with Lemonsqueezy
LEMONSQUEEZY_API_KEY=""
LEMONSQUEEZY_WEBHOOK_SECRET=""
LEMONSQUEEZY_STORE_ID=""
# ... with Stripe
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""
# ... with Chargebee
CHARGEBEE_SITE=""
CHARGEBEE_API_KEY=""
# ... with Creem
CREEM_API_KEY=""
CREEM_WEBHOOK_SECRET=""
# ... with Polar
POLAR_ACCESS_TOKEN=""
POLAR_WEBHOOK_SECRET=""

# Product price ids
NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY="asdf"
NEXT_PUBLIC_PRICE_ID_PRO_YEARLY="asdf"
NEXT_PUBLIC_PRICE_ID_LIFETIME="asdf"

# Analytics
# ... for Pirsch
NEXT_PUBLIC_PIRSCH_CODE=""
# ... for Plausible
NEXT_PUBLIC_PLAUSIBLE_URL=""
# ... for Mixpanel
NEXT_PUBLIC_MIXPANEL_TOKEN=""
# ... for Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=""

# Storage
S3_ACCESS_KEY_ID="872146b3cecdd108437e34f92d24e841"
S3_SECRET_ACCESS_KEY="a1a5a39a960af2537b47fc8db704bcbb94f8f4bdeb9f5d23d923741f92391ee5"
S3_ENDPOINT="https://okiqgwkzlomncicoufmv.supabase.co/storage/v1/s3"
S3_REGION="eu-central-1"
NEXT_PUBLIC_AVATARS_BUCKET_NAME="avatars"

# AI
# ... with OpenAI
OPENAI_API_KEY=""

# Admin password
ADMIN_PASSWORD="24YNPuph0cKpK0U-"